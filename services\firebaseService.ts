import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  User as FirebaseUser,
  updateProfile
} from 'firebase/auth';
import {
  doc,
  setDoc,
  getDoc,
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  getDocs,
  where,
  Timestamp
} from 'firebase/firestore';
import {
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject
} from 'firebase/storage';
import { auth, db, storage } from '../firebase';
import { User, Post, Comment, AdminMessage } from '../types';

// Auth functions
export const signUp = async (email: string, password: string, username: string): Promise<User | null> => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const firebaseUser = userCredential.user;

    // Update the user's display name
    await updateProfile(firebaseUser, { displayName: username });

    // Create user document in Firestore
    const userData: User = {
      id: firebaseUser.uid,
      username,
      avatarUrl: `https://picsum.photos/seed/${username.replace(/\s+/g, '')}/100/100`,
      isActive: false, // New users need approval
      isPendingApproval: true,
      bio: ''
    };

    await setDoc(doc(db, 'users', firebaseUser.uid), userData);

    return userData;
  } catch (error) {
    console.error('Error signing up:', error);
    throw error;
  }
};

export const signIn = async (email: string, password: string): Promise<FirebaseUser> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error) {
    console.error('Error signing in:', error);
    throw error;
  }
};

export const logOut = async (): Promise<void> => {
  try {
    await signOut(auth);
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

// User functions
export const getUserById = async (userId: string): Promise<User | null> => {
  try {
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (userDoc.exists()) {
      return userDoc.data() as User;
    }
    return null;
  } catch (error) {
    console.error('Error getting user:', error);
    throw error;
  }
};

export const updateUser = async (userId: string, userData: Partial<User>): Promise<void> => {
  try {
    await updateDoc(doc(db, 'users', userId), userData);
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
};

export const getAllUsers = async (): Promise<User[]> => {
  try {
    const usersQuery = query(collection(db, 'users'));
    const querySnapshot = await getDocs(usersQuery);
    return querySnapshot.docs.map(doc => doc.data() as User);
  } catch (error) {
    console.error('Error getting users:', error);
    throw error;
  }
};

export const getActiveUsers = async (): Promise<User[]> => {
  try {
    const usersQuery = query(
      collection(db, 'users'),
      where('isActive', '==', true)
    );
    const querySnapshot = await getDocs(usersQuery);
    return querySnapshot.docs.map(doc => doc.data() as User);
  } catch (error) {
    console.error('Error getting active users:', error);
    throw error;
  }
};

// Post functions
export const createPost = async (postData: Omit<Post, 'id' | 'timestamp' | 'likes' | 'comments'>): Promise<string> => {
  try {
    const post = {
      ...postData,
      timestamp: Timestamp.now().toDate().toISOString(),
      likes: 0,
      likedUsers: [], // Initialize empty array for liked users
      comments: []
    };

    const docRef = await addDoc(collection(db, 'posts'), post);
    console.log('Post created successfully with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Error creating post:', error);
    throw error;
  }
};

export const getAllPosts = async (): Promise<Post[]> => {
  try {
    // Simple query that orders by timestamp descending
    const postsQuery = query(
      collection(db, 'posts'),
      orderBy('timestamp', 'desc')
    );
    const querySnapshot = await getDocs(postsQuery);

    // Filter out placeholder documents and map to Post objects
    return querySnapshot.docs
      .filter(doc => !doc.data()._placeholder) // Filter out placeholder docs
      .map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Post));
  } catch (error) {
    console.error('Error getting posts:', error);
    throw error;
  }
};

export const getPostById = async (postId: string): Promise<Post | null> => {
  try {
    const postDoc = await getDoc(doc(db, 'posts', postId));
    if (postDoc.exists()) {
      return {
        id: postDoc.id,
        ...postDoc.data()
      } as Post;
    }
    return null;
  } catch (error) {
    console.error('Error getting post:', error);
    throw error;
  }
};

export const updatePost = async (postId: string, postData: Partial<Post>): Promise<void> => {
  try {
    await updateDoc(doc(db, 'posts', postId), postData);
  } catch (error) {
    console.error('Error updating post:', error);
    throw error;
  }
};

export const deletePost = async (postId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'posts', postId));
  } catch (error) {
    console.error('Error deleting post:', error);
    throw error;
  }
};

export const likePost = async (postId: string, userId: string): Promise<void> => {
  try {
    const postRef = doc(db, 'posts', postId);
    const postDoc = await getDoc(postRef);

    if (postDoc.exists()) {
      const postData = postDoc.data() as Post;
      const likedUsers = postData.likedUsers || [];

      if (!likedUsers.includes(userId)) {
        await updateDoc(postRef, {
          likes: (postData.likes || 0) + 1,
          likedUsers: [...likedUsers, userId]
        });
      }
    }
  } catch (error) {
    console.error('Error liking post:', error);
    throw error;
  }
};

export const unlikePost = async (postId: string, userId: string): Promise<void> => {
  try {
    const postRef = doc(db, 'posts', postId);
    const postDoc = await getDoc(postRef);

    if (postDoc.exists()) {
      const postData = postDoc.data() as Post;
      const likedUsers = postData.likedUsers || [];

      if (likedUsers.includes(userId)) {
        await updateDoc(postRef, {
          likes: Math.max((postData.likes || 0) - 1, 0),
          likedUsers: likedUsers.filter(id => id !== userId)
        });
      }
    }
  } catch (error) {
    console.error('Error unliking post:', error);
    throw error;
  }
};

// Get posts by user ID
export const getPostsByUserId = async (userId: string): Promise<Post[]> => {
  try {
    const postsQuery = query(
      collection(db, 'posts'),
      where('userId', '==', userId),
      orderBy('timestamp', 'desc')
    );
    const querySnapshot = await getDocs(postsQuery);

    return querySnapshot.docs
      .filter(doc => !doc.data()._placeholder)
      .map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Post));
  } catch (error) {
    console.error('Error getting posts by user ID:', error);
    throw error;
  }
};

// Get posts by tags
export const getPostsByTag = async (tag: string): Promise<Post[]> => {
  try {
    const postsQuery = query(
      collection(db, 'posts'),
      where('tags', 'array-contains', tag),
      orderBy('timestamp', 'desc')
    );
    const querySnapshot = await getDocs(postsQuery);

    return querySnapshot.docs
      .filter(doc => !doc.data()._placeholder)
      .map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Post));
  } catch (error) {
    console.error('Error getting posts by tag:', error);
    throw error;
  }
};

// Storage functions
export const uploadImage = async (file: File, path: string): Promise<string> => {
  try {
    const storageRef = ref(storage, path);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);
    return downloadURL;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

export const deleteImage = async (path: string): Promise<void> => {
  try {
    const storageRef = ref(storage, path);
    await deleteObject(storageRef);
  } catch (error) {
    console.error('Error deleting image:', error);
    throw error;
  }
};

// Admin message functions
export const createAdminMessage = async (messageData: Omit<AdminMessage, 'id' | 'timestamp' | 'isRead'>): Promise<string> => {
  try {
    const message = {
      ...messageData,
      timestamp: Timestamp.now().toDate().toISOString(),
      isRead: false
    };

    const docRef = await addDoc(collection(db, 'adminMessages'), message);
    return docRef.id;
  } catch (error) {
    console.error('Error creating admin message:', error);
    throw error;
  }
};

export const getAllAdminMessages = async (): Promise<AdminMessage[]> => {
  try {
    const messagesQuery = query(
      collection(db, 'adminMessages'),
      where('_placeholder', '!=', true),
      orderBy('_placeholder'),
      orderBy('timestamp', 'desc')
    );
    const querySnapshot = await getDocs(messagesQuery);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as AdminMessage));
  } catch (error) {
    console.error('Error getting admin messages:', error);
    // Fallback query
    try {
      const fallbackQuery = query(collection(db, 'adminMessages'), orderBy('timestamp', 'desc'));
      const fallbackSnapshot = await getDocs(fallbackQuery);
      return fallbackSnapshot.docs
        .filter(doc => !doc.data()._placeholder)
        .map(doc => ({
          id: doc.id,
          ...doc.data()
        } as AdminMessage));
    } catch (fallbackError) {
      console.error('Fallback query also failed:', fallbackError);
      return [];
    }
  }
};

export const markAdminMessageAsRead = async (messageId: string): Promise<void> => {
  try {
    await updateDoc(doc(db, 'adminMessages', messageId), { isRead: true });
  } catch (error) {
    console.error('Error marking message as read:', error);
    throw error;
  }
};

export const deleteAdminMessage = async (messageId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'adminMessages', messageId));
  } catch (error) {
    console.error('Error deleting admin message:', error);
    throw error;
  }
};

// Login screen config functions
export const getLoginScreenConfig = async (): Promise<any> => {
  try {
    const configDoc = await getDoc(doc(db, 'config', 'loginScreen'));
    if (configDoc.exists()) {
      return configDoc.data();
    }
    return null;
  } catch (error) {
    console.error('Error getting login screen config:', error);
    throw error;
  }
};

export const updateLoginScreenConfig = async (config: any): Promise<void> => {
  try {
    await setDoc(doc(db, 'config', 'loginScreen'), config);
  } catch (error) {
    console.error('Error updating login screen config:', error);
    throw error;
  }
};

// Admin functions
export const createAdminUser = async (email: string, password: string, username: string): Promise<User> => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const firebaseUser = userCredential.user;

    // Update the user's display name
    await updateProfile(firebaseUser, { displayName: username });

    // Create admin user document in Firestore
    const userData: User = {
      id: firebaseUser.uid,
      username,
      avatarUrl: `https://picsum.photos/seed/${username.replace(/\s+/g, '')}/100/100`,
      isActive: true,
      isPendingApproval: false,
      isAdmin: true,
      bio: 'Platform Administrator\nGuardian of the digital realm\nMaintaining order in chaos\n⚠️ With great power...'
    };

    await setDoc(doc(db, 'users', firebaseUser.uid), userData);

    return userData;
  } catch (error) {
    console.error('Error creating admin user:', error);
    throw error;
  }
};
