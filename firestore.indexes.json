{"indexes": [{"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tags", "arrayConfig": "CONTAINS"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "posts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isAnonymous", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "isPendingApproval", "order": "ASCENDING"}]}, {"collectionGroup": "adminMessages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isRead", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}], "fieldOverrides": []}